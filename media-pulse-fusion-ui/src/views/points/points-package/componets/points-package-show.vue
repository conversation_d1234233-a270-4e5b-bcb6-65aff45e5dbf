<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { NCard, NGrid, NGi, NTag, NButton, NIcon, NSpin, NEmpty, useMessage } from 'naive-ui';
import { fetchGetPointsPackageList } from '@/service/api/points/points-package';

defineOptions({
  name: 'PointsPackageShow'
});

const message = useMessage();
const loading = ref(false);
const packageList = ref<any[]>([]);
const error = ref<string>('');

// 获取积分套餐列表
async function getPackageList() {
  loading.value = true;
  error.value = '';
  try {
    const { data, error: apiError } = await fetchGetPointsPackageList({
      pageNum: 1,
      pageSize: 100,
      status: '0' // 只获取正常状态的套餐
    });

    if (!apiError && data) {
      // 按照显示顺序排序
      packageList.value = data.rows.sort((a: any, b: any) => (a.sortOrder || 0) - (b.sortOrder || 0));
    } else {
      error.value = '获取积分套餐失败';
      message.error('获取积分套餐失败');
    }
  } catch (err) {
    error.value = '网络错误，请稍后重试';
    message.error('网络错误，请稍后重试');
  } finally {
    loading.value = false;
  }
}

// 套餐类型映射
const packageTypeMap = {
  '1': { label: '普通套餐', color: 'default' },
  '2': { label: '限时优惠', color: 'warning' },
  '3': { label: '新用户专享', color: 'success' }
};

// 格式化价格
function formatPrice(price: number) {
  return (price / 100).toFixed(2);
}

// 计算折扣信息
function getDiscountInfo(originalPrice: number, salePrice: number) {
  if (originalPrice <= salePrice) return null;
  const discount = Math.round((1 - salePrice / originalPrice) * 10);
  return `${discount}折`;
}

// 检查是否在有效期内
function isValidPackage(pkg: any) {
  const now = new Date();
  const startTime = pkg.startTime ? new Date(pkg.startTime) : null;
  const endTime = pkg.endTime ? new Date(pkg.endTime) : null;

  if (startTime && now < startTime) return false;
  if (endTime && now > endTime) return false;
  return true;
}

// 过滤有效的套餐
const validPackages = computed(() => {
  return packageList.value.filter(pkg => isValidPackage(pkg));
});

// 处理购买按钮点击
function handleBuyPackage(pkg: any) {
  message.info(`即将购买套餐：${pkg.packageName}`);
  // TODO: 这里可以添加购买逻辑，比如跳转到支付页面或打开支付弹窗
}

onMounted(() => {
  getPackageList();
});
</script>

<template>
  <div class="points-package-container">
    <div class="header-section">
      <h1 class="page-title">积分套餐</h1>
      <p class="page-subtitle">选择适合您的积分套餐，享受更多精彩内容</p>
    </div>

    <NSpin :show="loading" size="large">
      <template #description>
        <div class="loading-text">正在加载积分套餐...</div>
      </template>
      <div v-if="validPackages.length === 0 && !loading" class="empty-container">
        <NEmpty :description="error || '暂无可用的积分套餐'">
          <template v-if="error" #extra>
            <NButton @click="getPackageList">重试</NButton>
          </template>
        </NEmpty>
      </div>

      <NGrid v-else cols="1 s:1 m:1 l:1 xl:1 2xl:1" :x-gap="24" :y-gap="24" responsive="screen" class="package-grid">
        <NGi v-for="pkg in validPackages" :key="pkg.id">
          <NCard
            class="package-card"
            :class="{ 'hot-package': pkg.isHot === '1' }"
            hoverable
          >
            <!-- 热门标签 -->
            <div v-if="pkg.isHot === '1'" class="hot-badge">
              <NIcon size="16">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </NIcon>
              热门
            </div>

            <div class="package-content">
              <!-- 左侧信息 -->
              <div class="package-info">
                <div class="package-header">
                  <h3 class="package-name">{{ pkg.packageName }}</h3>
                  <div class="package-tags">
                    <NTag
                      :type="packageTypeMap[pkg.packageType]?.color || 'default'"
                      size="small"
                    >
                      {{ packageTypeMap[pkg.packageType]?.label || '未知类型' }}
                    </NTag>
                    <NTag
                      v-if="getDiscountInfo(pkg.originalPrice, pkg.salePrice)"
                      type="error"
                      size="small"
                    >
                      {{ getDiscountInfo(pkg.originalPrice, pkg.salePrice) }}
                    </NTag>
                  </div>
                </div>

                <p class="package-desc">{{ pkg.packageDesc }}</p>

                <div class="package-details">
                  <div class="detail-item">
                    <span class="detail-label">积分数量：</span>
                    <span class="detail-value highlight">{{ pkg.pointsAmount?.toLocaleString() }}</span>
                  </div>
                  <div v-if="pkg.bonusPoints && pkg.bonusPoints > 0" class="detail-item">
                    <span class="detail-label">赠送积分：</span>
                    <span class="detail-value bonus">+{{ pkg.bonusPoints?.toLocaleString() }}</span>
                  </div>
                </div>
              </div>

              <!-- 右侧价格和购买 -->
              <div class="package-action">
                <div class="price-section">
                  <div v-if="pkg.originalPrice !== pkg.salePrice" class="original-price">
                    ¥{{ formatPrice(pkg.originalPrice) }}
                  </div>
                  <div class="sale-price">
                    {{ formatPrice(pkg.salePrice) }}
                  </div>
                </div>

                <NButton
                  type="primary"
                  size="large"
                  class="buy-button"
                  :class="{ 'hot-button': pkg.isHot === '1' }"
                  @click="handleBuyPackage(pkg)"
                >
                  立即购买
                </NButton>
              </div>
            </div>
          </NCard>
        </NGi>
      </NGrid>
    </NSpin>
  </div>
</template>

<style scoped>
.points-package-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.loading-text {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.package-grid {
  max-width: 1200px;
  margin: 0 auto;
}

.package-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.package-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.package-card:active {
  transform: translateY(-2px);
}

.package-card.hot-package {
  border-color: #ff6b6b;
  background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
}

.hot-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.package-content {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 24px;
}

.package-info {
  flex: 1;
}

.package-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.package-name {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
}

.package-tags {
  display: flex;
  gap: 8px;
}

.package-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0 0 20px 0;
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 14px;
  color: #666;
  min-width: 80px;
}

.detail-value {
  font-size: 16px;
  font-weight: 600;
}

.detail-value.highlight {
  color: #667eea;
  font-size: 18px;
}

.detail-value.bonus {
  color: #ff6b6b;
}

.package-action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 16px;
}

.price-section {
  text-align: right;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
  margin-bottom: 4px;
}

.sale-price {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  position: relative;
}

.sale-price::before {
  content: '¥';
  font-size: 18px;
  position: absolute;
  left: -12px;
  top: 2px;
}

.buy-button {
  min-width: 120px;
  height: 44px;
  border-radius: 22px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.buy-button.hot-button {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
}

.buy-button.hot-button:hover {
  background: linear-gradient(135deg, #ee5a52 0%, #dc4c41 100%);
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .points-package-container {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .package-content {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .package-action {
    align-items: stretch;
  }

  .price-section {
    text-align: center;
  }

  .buy-button {
    width: 100%;
  }

  .package-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
